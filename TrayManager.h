#ifndef TRAYMANAGER_H
#define TRAYMANAGER_H

#include <QObject>
#include <QSystemTrayIcon>
#include <QMenu>
#include <QAction>
#include <QApplication>
#include <QMessageBox>
#include <QTimer>

// 前置声明
class CDlgMain_MonitorWhiteCtrlProgram;
class WhiteListManager;

/**
 * @brief 系统托盘管理器
 * 
 * 提供系统托盘图标和右键菜单功能
 * 包含白名单管理、程序控制等功能
 */
class TrayManager : public QObject
{
    Q_OBJECT

public:
    explicit TrayManager(CDlgMain_MonitorWhiteCtrlProgram *mainWindow, QObject *parent = nullptr);
    ~TrayManager();

    // 显示/隐藏托盘图标
    void showTrayIcon();
    void hideTrayIcon();
    
    // 检查系统是否支持托盘
    bool isSystemTrayAvailable();
    
    // 更新托盘状态
    void updateTrayStatus(const QString &status);

    // 更新服务菜单状态
    void updateServiceMenuStatus(bool serviceInstalled, bool serviceRunning);

    // 显示托盘消息
    void showMessage(const QString &title, const QString &message,
                    QSystemTrayIcon::MessageIcon icon = QSystemTrayIcon::Information);

public slots:
    // 托盘图标被激活
    void onTrayIconActivated(QSystemTrayIcon::ActivationReason reason);
    
    // 菜单项槽函数
    void onShowMainWindow();
    void onHideMainWindow();
    void onClearWhiteList();
    void onSyncWhiteList();
    void onStartService();      // 开启服务
    void onUninstallService();  // 卸载服务
    void onDeleteService();     // 删除服务
    void onAbout();
    void onExit();
    
    // 白名单状态更新
    void onWhiteListUpdated(int count);

signals:
    // 请求清空白名单
    void clearWhiteListRequested();

    // 请求同步白名单
    void syncWhiteListRequested();

    // 请求开启服务
    void startServiceRequested();

    // 请求卸载服务
    void uninstallServiceRequested();

    // 请求删除服务
    void deleteServiceRequested();

private:
    void createTrayIcon();
    void createTrayMenu();

private:
    CDlgMain_MonitorWhiteCtrlProgram *m_mainWindow;
    QSystemTrayIcon *m_trayIcon;
    QMenu *m_trayMenu;
    
    // 菜单项
    QAction *m_showAction;
    QAction *m_hideAction;
    QAction *m_clearWhiteListAction;
    QAction *m_syncWhiteListAction;
    QAction *m_startServiceAction;      // 开启服务
    QAction *m_uninstallServiceAction;  // 卸载服务
    QAction *m_deleteServiceAction;     // 删除服务
    QAction *m_aboutAction;
    QAction *m_exitAction;
    
    // 状态信息
    QString m_currentStatus;
    int m_whiteListCount;
};

#endif // TRAYMANAGER_H
