#include "ServiceManager.h"
#include <QDebug>
#include <QMessageBox>
#include <QFileInfo>
#include <QSettings>
#include <QThread>

#ifdef Q_OS_WIN
#include <shellapi.h>
#include <lmcons.h>
#endif

// 静态常量定义
const QString ServiceManager::DEFAULT_SERVICE_NAME = "MonitorWhiteCtrlService";
const QString ServiceManager::DEFAULT_DISPLAY_NAME = "Monitor Control Service";
const QString ServiceManager::DEFAULT_DESCRIPTION = "Monitor whitelist control service, monitors display EDID information and USB devices";
const QString ServiceManager::REGISTRY_RUN_KEY = "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run";

ServiceManager::ServiceManager(QObject *parent)
    : QObject(parent)
    , m_serviceName(DEFAULT_SERVICE_NAME)
    , m_serviceDisplayName(DEFAULT_DISPLAY_NAME)
    , m_serviceDescription(DEFAULT_DESCRIPTION)
{
    m_executablePath = getCurrentExecutablePath();
    qDebug() << "ServiceManager initialized";
    qDebug() << "Executable path:" << m_executablePath;
}

ServiceManager::~ServiceManager()
{
}

QString ServiceManager::getCurrentExecutablePath()
{
    return QCoreApplication::applicationFilePath();
}

bool ServiceManager::isRunningAsAdmin()
{
#ifdef Q_OS_WIN
    BOOL isAdmin = FALSE;
    PSID adminGroup = nullptr;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
    
    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(nullptr, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }
    
    return isAdmin == TRUE;
#else
    return false;
#endif
}

bool ServiceManager::requestAdminPrivileges()
{
#ifdef Q_OS_WIN
    if (isRunningAsAdmin()) {
        return true;
    }
    
    QString program = QCoreApplication::applicationFilePath();
    QString arguments = "--admin-mode";
    
    // 使用ShellExecute请求管理员权限
    HINSTANCE result = ShellExecute(nullptr, L"runas", 
                                   reinterpret_cast<LPCWSTR>(program.utf16()),
                                   reinterpret_cast<LPCWSTR>(arguments.utf16()),
                                   nullptr, SW_SHOWNORMAL);
    
    return reinterpret_cast<int>(result) > 32;
#else
    return false;
#endif
}

bool ServiceManager::setAutoStart(bool enable)
{
    if (enable) {
        qDebug() << "Enabling auto-start on boot";
    } else {
        qDebug() << "Disabling auto-start on boot";
    }
    
    return setRegistryAutoStart(enable);
}

bool ServiceManager::isAutoStartEnabled()
{
    return getRegistryAutoStart();
}

bool ServiceManager::setRegistryAutoStart(bool enable)
{
    QSettings settings(REGISTRY_RUN_KEY, QSettings::NativeFormat);
    
    if (enable) {
        // 添加到启动项
        QString startupCommand = QString("\"%1\" --startup").arg(m_executablePath);
        settings.setValue(m_serviceName, startupCommand);
        qDebug() << "Added to registry startup:" << startupCommand;
    } else {
        // 从启动项移除
        settings.remove(m_serviceName);
        qDebug() << "Removed from registry startup";
    }
    
    settings.sync();
    return settings.status() == QSettings::NoError;
}

bool ServiceManager::getRegistryAutoStart()
{
    QSettings settings(REGISTRY_RUN_KEY, QSettings::NativeFormat);
    return settings.contains(m_serviceName);
}

void ServiceManager::setServiceName(const QString &serviceName)
{
    m_serviceName = serviceName;
}

void ServiceManager::setServiceDisplayName(const QString &displayName)
{
    m_serviceDisplayName = displayName;
}

void ServiceManager::setServiceDescription(const QString &description)
{
    m_serviceDescription = description;
}

QString ServiceManager::getServiceName() const
{
    return m_serviceName;
}

QString ServiceManager::getServiceDisplayName() const
{
    return m_serviceDisplayName;
}

QString ServiceManager::getServiceDescription() const
{
    return m_serviceDescription;
}

#ifdef Q_OS_WIN
bool ServiceManager::installService()
{
    if (!isRunningAsAdmin()) {
        qWarning() << "Installing service requires administrator privileges";
        return false;
    }
    
    SC_HANDLE hSCManager = openServiceManager();
    if (!hSCManager) {
        qWarning() << "Unable to open service control manager";
        return false;
    }
    
    // 检查服务是否已存在
    SC_HANDLE hService = openService(hSCManager);
    if (hService) {
        qDebug() << "Service already exists:" << m_serviceName;
        closeServiceHandle(hService);
        closeServiceHandle(hSCManager);
        return true;
    }
    
    // 创建服务
    QString serviceCommand = QString("\"%1\" --service").arg(m_executablePath);
    
    hService = CreateService(
        hSCManager,
        reinterpret_cast<LPCWSTR>(m_serviceName.utf16()),
        reinterpret_cast<LPCWSTR>(m_serviceDisplayName.utf16()),
        SERVICE_ALL_ACCESS,
        SERVICE_WIN32_OWN_PROCESS,
        SERVICE_AUTO_START,
        SERVICE_ERROR_NORMAL,
        reinterpret_cast<LPCWSTR>(serviceCommand.utf16()),
        nullptr, nullptr, nullptr, nullptr, nullptr
    );
    
    if (!hService) {
        DWORD error = GetLastError();
        qWarning() << "Failed to create service, error code:" << error;
        closeServiceHandle(hSCManager);
        return false;
    }
    
    // 设置服务描述
    SERVICE_DESCRIPTION sd;
    sd.lpDescription = const_cast<LPWSTR>(reinterpret_cast<LPCWSTR>(m_serviceDescription.utf16()));
    ChangeServiceConfig2(hService, SERVICE_CONFIG_DESCRIPTION, &sd);
    
    qDebug() << "Service installed successfully:" << m_serviceName;
    
    closeServiceHandle(hService);
    closeServiceHandle(hSCManager);
    
    emit serviceInstalled();
    return true;
}

bool ServiceManager::uninstallService()
{
    qDebug() << "Starting complete service uninstallation...";

    if (!isRunningAsAdmin()) {
        qWarning() << "Uninstalling service requires administrator privileges";
        return false;
    }

    bool success = true;

    // 1. 停止并删除Windows服务
    if (isServiceInstalled()) {
        qDebug() << "Stopping and removing Windows service...";

        SC_HANDLE hSCManager = openServiceManager();
        if (!hSCManager) {
            qWarning() << "Failed to open service control manager";
            return false;
        }

        SC_HANDLE hService = openService(hSCManager);
        if (hService) {
            // 停止服务
            qDebug() << "Stopping service...";
            SERVICE_STATUS status;
            if (ControlService(hService, SERVICE_CONTROL_STOP, &status)) {
                qDebug() << "Service stopped successfully";

                // 等待服务完全停止
                int retryCount = 0;
                while (retryCount < 10) {
                    if (QueryServiceStatus(hService, &status)) {
                        if (status.dwCurrentState == SERVICE_STOPPED) {
                            qDebug() << "Service fully stopped";
                            break;
                        }
                        QThread::msleep(500); // 等待500ms
                        retryCount++;
                    } else {
                        break;
                    }
                }
            } else {
                DWORD error = GetLastError();
                if (error == ERROR_SERVICE_NOT_ACTIVE) {
                    qDebug() << "Service was already stopped";
                } else {
                    qWarning() << "Failed to stop service, error code:" << error;
                }
            }

            // 删除服务
            qDebug() << "Deleting service...";
            if (DeleteService(hService)) {
                qDebug() << "Windows service deleted successfully:" << m_serviceName;
            } else {
                DWORD error = GetLastError();
                qWarning() << "Failed to delete service, error code:" << error;
                success = false;
            }

            closeServiceHandle(hService);
        } else {
            qDebug() << "Service does not exist:" << m_serviceName;
        }

        closeServiceHandle(hSCManager);
    } else {
        qDebug() << "Windows service is not installed";
    }

    // 2. 删除注册表自启动项
    qDebug() << "Removing registry auto-start entry...";
    if (setAutoStart(false)) {
        qDebug() << "Registry auto-start entry removed successfully";
    } else {
        qWarning() << "Failed to remove registry auto-start entry";
        success = false;
    }

    // 3. 验证卸载结果
    qDebug() << "Verifying uninstallation...";
    if (!isServiceInstalled() && !isAutoStartEnabled()) {
        qDebug() << "Complete service uninstallation successful";
        emit serviceUninstalled();
        return true;
    } else {
        if (isServiceInstalled()) {
            qWarning() << "Windows service still exists after uninstallation";
        }
        if (isAutoStartEnabled()) {
            qWarning() << "Auto-start entry still exists after removal";
        }
        return success;
    }
}

bool ServiceManager::completeUninstall()
{
    qDebug() << "Starting complete service uninstallation with cleanup...";

    bool success = true;

    // 1. 首先执行标准卸载
    if (!uninstallService()) {
        qWarning() << "Standard uninstall failed";
        success = false;
    }

    // 2. 清理可能的残留注册表项
    qDebug() << "Cleaning up registry entries...";

    // 清理HKCU Run项
    QSettings userRun("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", QSettings::NativeFormat);
    if (userRun.contains(m_serviceName)) {
        userRun.remove(m_serviceName);
        qDebug() << "Removed HKCU Run entry:" << m_serviceName;
    }

    // 清理HKLM Run项（如果存在）
    QSettings systemRun("HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", QSettings::NativeFormat);
    if (systemRun.contains(m_serviceName)) {
        systemRun.remove(m_serviceName);
        qDebug() << "Removed HKLM Run entry:" << m_serviceName;
    }

    // 3. 清理可能的服务相关注册表项
#ifdef Q_OS_WIN
    qDebug() << "Cleaning up service registry entries...";

    // 清理服务注册表项（通常在卸载服务时会自动清理，但确保清理完整）
    QString serviceKeyPath = QString("SYSTEM\\CurrentControlSet\\Services\\%1").arg(m_serviceName);
    QSettings serviceReg("HKEY_LOCAL_MACHINE\\" + serviceKeyPath, QSettings::NativeFormat);

    // 检查服务注册表项是否还存在
    QStringList keys = serviceReg.allKeys();
    if (!keys.isEmpty()) {
        qWarning() << "Service registry entries still exist after uninstall";
        // 注意：通常不建议手动删除服务注册表项，因为这可能导致系统不稳定
        // 这里只是检查和报告
    }
#endif

    // 4. 验证清理结果
    qDebug() << "Verifying complete cleanup...";

    bool serviceClean = !isServiceInstalled();
    bool autoStartClean = !isAutoStartEnabled();

    if (serviceClean && autoStartClean) {
        qDebug() << "Complete uninstallation and cleanup successful";
        emit serviceUninstalled();
        return true;
    } else {
        if (!serviceClean) {
            qWarning() << "Service still exists after complete uninstall";
        }
        if (!autoStartClean) {
            qWarning() << "Auto-start entries still exist after cleanup";
        }
        return success;
    }
}

bool ServiceManager::forceDeleteService()
{
    qDebug() << "Starting FORCE DELETE service operation...";

    if (!isRunningAsAdmin()) {
        qWarning() << "Force deleting service requires administrator privileges";
        return false;
    }

    bool success = true;

#ifdef Q_OS_WIN
    // 1. 强制终止可能的服务进程
    qDebug() << "Force terminating service processes...";

    // 使用taskkill强制终止进程
    QString executableName = QFileInfo(m_executablePath).baseName() + ".exe";
    QProcess killProcess;
    killProcess.start("taskkill", QStringList() << "/F" << "/IM" << executableName);
    killProcess.waitForFinished(5000);

    if (killProcess.exitCode() == 0) {
        qDebug() << "Service processes terminated successfully";
    } else {
        qDebug() << "No service processes found or termination failed";
    }

    // 2. 强制停止并删除Windows服务
    qDebug() << "Force deleting Windows service...";

    SC_HANDLE hSCManager = openServiceManager();
    if (hSCManager) {
        SC_HANDLE hService = openService(hSCManager);
        if (hService) {
            // 强制停止服务（忽略错误）
            SERVICE_STATUS status;
            ControlService(hService, SERVICE_CONTROL_STOP, &status);

            // 等待一段时间
            QThread::msleep(1000);

            // 强制删除服务
            if (DeleteService(hService)) {
                qDebug() << "Windows service force deleted successfully";
            } else {
                DWORD error = GetLastError();
                qWarning() << "Failed to force delete service, error code:" << error;
                success = false;
            }

            closeServiceHandle(hService);
        }
        closeServiceHandle(hSCManager);
    }

    // 3. 强制清理注册表项
    qDebug() << "Force cleaning registry entries...";

    // 清理所有可能的启动项
    QStringList runKeys = {
        "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
        "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
        "HKEY_LOCAL_MACHINE\\Software\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Run"
    };

    for (const QString &keyPath : runKeys) {
        QSettings runSettings(keyPath, QSettings::NativeFormat);
        if (runSettings.contains(m_serviceName)) {
            runSettings.remove(m_serviceName);
            qDebug() << "Force removed run entry from:" << keyPath;
        }
    }

    // 4. 尝试强制删除服务注册表项（高风险操作）
    qDebug() << "Attempting to force clean service registry entries...";

    // 使用reg命令强制删除服务注册表项
    QString serviceKeyPath = QString("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\%1").arg(m_serviceName);
    QProcess regProcess;
    regProcess.start("reg", QStringList() << "delete" << serviceKeyPath << "/f");
    regProcess.waitForFinished(5000);

    if (regProcess.exitCode() == 0) {
        qDebug() << "Service registry key force deleted successfully";
    } else {
        qDebug() << "Service registry key deletion failed or key doesn't exist";
    }

    // 5. 清理可能的临时文件和配置
    qDebug() << "Cleaning up temporary files and configurations...";

    // 清理可能的日志文件
    QString logDir = QDir::tempPath() + "/" + m_serviceName;
    QDir tempDir(logDir);
    if (tempDir.exists()) {
        if (tempDir.removeRecursively()) {
            qDebug() << "Temporary directory cleaned:" << logDir;
        }
    }

    // 6. 最终验证
    qDebug() << "Verifying force deletion...";

    // 等待一段时间让系统更新
    QThread::msleep(2000);

    bool serviceClean = !isServiceInstalled();
    bool autoStartClean = !isAutoStartEnabled();

    if (serviceClean && autoStartClean) {
        qDebug() << "Force deletion completed successfully";
        emit serviceUninstalled();
        return true;
    } else {
        if (!serviceClean) {
            qWarning() << "Service still exists after force deletion";
        }
        if (!autoStartClean) {
            qWarning() << "Auto-start entries still exist after force cleanup";
        }

        // 即使部分失败，如果主要目标达成也返回成功
        if (serviceClean) {
            qDebug() << "Force deletion partially successful (service removed)";
            emit serviceUninstalled();
            return true;
        }

        return false;
    }
#else
    return false;
#endif
}

bool ServiceManager::startService()
{
    SC_HANDLE hSCManager = openServiceManager();
    if (!hSCManager) {
        return false;
    }
    
    SC_HANDLE hService = openService(hSCManager);
    if (!hService) {
        closeServiceHandle(hSCManager);
        return false;
    }
    
    BOOL result = StartService(hService, 0, nullptr);
    
    closeServiceHandle(hService);
    closeServiceHandle(hSCManager);
    
    if (result) {
        qDebug() << "Service started successfully:" << m_serviceName;
        emit serviceStarted();
        return true;
    } else {
        DWORD error = GetLastError();
        if (error == ERROR_SERVICE_ALREADY_RUNNING) {
            qDebug() << "Service is already running:" << m_serviceName;
            return true;
        }
        qWarning() << "Service start failed, error code:" << error;
        return false;
    }
}

bool ServiceManager::stopService()
{
    SC_HANDLE hSCManager = openServiceManager();
    if (!hSCManager) {
        return false;
    }
    
    SC_HANDLE hService = openService(hSCManager);
    if (!hService) {
        closeServiceHandle(hSCManager);
        return false;
    }
    
    SERVICE_STATUS status;
    BOOL result = ControlService(hService, SERVICE_CONTROL_STOP, &status);
    
    closeServiceHandle(hService);
    closeServiceHandle(hSCManager);
    
    if (result) {
        qDebug() << "Service stopped successfully:" << m_serviceName;
        emit serviceStopped();
        return true;
    } else {
        qWarning() << "Service stop failed";
        return false;
    }
}

bool ServiceManager::restartService()
{
    return stopService() && startService();
}

ServiceManager::ServiceStatus ServiceManager::getServiceStatus()
{
    SC_HANDLE hSCManager = openServiceManager();
    if (!hSCManager) {
        return ServiceUnknown;
    }
    
    SC_HANDLE hService = openService(hSCManager);
    if (!hService) {
        closeServiceHandle(hSCManager);
        return ServiceNotInstalled;
    }
    
    SERVICE_STATUS status;
    if (!QueryServiceStatus(hService, &status)) {
        closeServiceHandle(hService);
        closeServiceHandle(hSCManager);
        return ServiceUnknown;
    }
    
    closeServiceHandle(hService);
    closeServiceHandle(hSCManager);
    
    return convertWindowsStatus(status.dwCurrentState);
}

QString ServiceManager::getServiceStatusString()
{
    ServiceStatus status = getServiceStatus();
    
    switch (status) {
    case ServiceNotInstalled:   return "未安装";
    case ServiceStopped:        return "已停止";
    case ServiceStartPending:   return "启动中";
    case ServiceStopPending:    return "停止中";
    case ServiceRunning:        return "运行中";
    case ServiceContinuePending: return "继续中";
    case ServicePausePending:   return "暂停中";
    case ServicePaused:         return "已暂停";
    case ServiceUnknown:        return "未知";
    default:                    return "未知";
    }
}

bool ServiceManager::isServiceInstalled()
{
    return getServiceStatus() != ServiceNotInstalled;
}

bool ServiceManager::isServiceRunning()
{
    return getServiceStatus() == ServiceRunning;
}

SC_HANDLE ServiceManager::openServiceManager()
{
    return OpenSCManager(nullptr, nullptr, SC_MANAGER_ALL_ACCESS);
}

SC_HANDLE ServiceManager::openService(SC_HANDLE hSCManager)
{
    return OpenService(hSCManager, reinterpret_cast<LPCWSTR>(m_serviceName.utf16()), SERVICE_ALL_ACCESS);
}

void ServiceManager::closeServiceHandle(SC_HANDLE handle)
{
    if (handle) {
        CloseServiceHandle(handle);
    }
}

ServiceManager::ServiceStatus ServiceManager::convertWindowsStatus(DWORD dwCurrentState)
{
    switch (dwCurrentState) {
    case SERVICE_STOPPED:           return ServiceStopped;
    case SERVICE_START_PENDING:     return ServiceStartPending;
    case SERVICE_STOP_PENDING:      return ServiceStopPending;
    case SERVICE_RUNNING:           return ServiceRunning;
    case SERVICE_CONTINUE_PENDING:  return ServiceContinuePending;
    case SERVICE_PAUSE_PENDING:     return ServicePausePending;
    case SERVICE_PAUSED:            return ServicePaused;
    default:                        return ServiceUnknown;
    }
}

#else
// 非Windows平台的空实现
bool ServiceManager::installService() { return false; }
bool ServiceManager::uninstallService() { return false; }
bool ServiceManager::completeUninstall() { return false; }
bool ServiceManager::forceDeleteService() { return false; }
bool ServiceManager::startService() { return false; }
bool ServiceManager::stopService() { return false; }
bool ServiceManager::restartService() { return false; }
ServiceManager::ServiceStatus ServiceManager::getServiceStatus() { return ServiceNotInstalled; }
QString ServiceManager::getServiceStatusString() { return "不支持"; }
bool ServiceManager::isServiceInstalled() { return false; }
bool ServiceManager::isServiceRunning() { return false; }
#endif
