#include "WhiteListManager.h"
#include <QJsonParseError>
#include <QFileInfo>
#include <QStorageInfo>
#include <QDirIterator>
#include <QDebug>

// 静态常量定义
const QString WhiteListManager::WHITE_LIST_FILE_NAME = "monitor_whitelist.json";
const QString WhiteListManager::USB_WHITE_LIST_FILE_NAME = "monitor_whitelist.json";
const int WhiteListManager::MAX_BACKUP_FILES = 10;

WhiteListManager::WhiteListManager(QObject *parent)
    : QObject(parent)
{
    // 设置白名单文件路径
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(appDataPath);
    m_whiteListFile = QDir(appDataPath).filePath(WHITE_LIST_FILE_NAME);
    
    // 加载现有白名单
    loadFromFile();
    
    qDebug() << "WhiteList manager initialized, file path:" << m_whiteListFile;
    qDebug() << "Current whitelist contains" << m_whiteList.size() << "entries";
}

WhiteListManager::~WhiteListManager()
{
    saveToFile();
}

bool WhiteListManager::addToWhiteList(const DisplayInfo &displayInfo, const QString &source)
{
    QMutexLocker locker(&m_mutex);
    
    if (!displayInfo.isValid) {
        qWarning() << "Attempting to add invalid display info to whitelist";
        return false;
    }
    
    WhiteListEntry entry(displayInfo, source);
    
    // 检查是否已存在
    QString uniqueId = entry.getUniqueId();
    for (const WhiteListEntry &existing : m_whiteList) {
        if (existing.getUniqueId() == uniqueId) {
            qDebug() << "Monitor already in whitelist:" << uniqueId;
            return false;
        }
    }
    
    // 添加到白名单
    m_whiteList.append(entry);
    
    qDebug() << "Added monitor to whitelist:" << uniqueId << "source:" << source;
    
    // 保存到文件
    bool saved = saveToFile();
    
    if (saved) {
        emit whiteListUpdated(m_whiteList.size());
    }
    
    return saved;
}

bool WhiteListManager::removeFromWhiteList(const QString &uniqueId)
{
    QMutexLocker locker(&m_mutex);
    
    for (int i = 0; i < m_whiteList.size(); ++i) {
        if (m_whiteList[i].getUniqueId() == uniqueId) {
            m_whiteList.removeAt(i);
            qDebug() << "从白名单移除显示器:" << uniqueId;
            
            bool saved = saveToFile();
            if (saved) {
                emit whiteListUpdated(m_whiteList.size());
            }
            return saved;
        }
    }
    
    qWarning() << "要移除的显示器不在白名单中:" << uniqueId;
    return false;
}

void WhiteListManager::clearWhiteList()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_whiteList.isEmpty()) {
        qDebug() << "Whitelist is already empty";
        return;
    }
    
    // 创建备份
    createBackup();
    
    int oldCount = m_whiteList.size();
    m_whiteList.clear();
    
    qDebug() << "Cleared whitelist, previously had" << oldCount << "entries";
    
    bool saved = saveToFile();
    if (saved) {
        emit whiteListCleared();
        emit whiteListUpdated(0);
    }
}

bool WhiteListManager::isInWhiteList(const DisplayInfo &displayInfo) const
{
    return isInWhiteList(displayInfo.manufacturer, displayInfo.productCode, displayInfo.serialNumber);
}

bool WhiteListManager::isInWhiteList(const QString &manufacturer, const QString &productCode, const QString &serialNumber) const
{
    QMutexLocker locker(&m_mutex);
    
    QString uniqueId = QString("%1_%2_%3").arg(manufacturer).arg(productCode).arg(serialNumber);
    
    for (const WhiteListEntry &entry : m_whiteList) {
        if (entry.getUniqueId() == uniqueId) {
            return true;
        }
    }
    
    return false;
}

QList<WhiteListManager::WhiteListEntry> WhiteListManager::getAllEntries() const
{
    QMutexLocker locker(&m_mutex);
    return m_whiteList;
}

int WhiteListManager::getWhiteListCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_whiteList.size();
}

bool WhiteListManager::saveToFile()
{
    QJsonArray jsonArray;
    
    for (const WhiteListEntry &entry : m_whiteList) {
        jsonArray.append(entry.toJson());
    }
    
    QJsonObject rootObject;
    rootObject["version"] = "1.0";
    rootObject["lastModified"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    rootObject["count"] = m_whiteList.size();
    rootObject["whiteList"] = jsonArray;
    
    QJsonDocument doc(rootObject);
    
    QFile file(m_whiteListFile);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Unable to open whitelist file for writing:" << m_whiteListFile;
        return false;
    }
    
    qint64 bytesWritten = file.write(doc.toJson());
    file.close();
    
    if (bytesWritten == -1) {
        qWarning() << "Failed to write whitelist file";
        return false;
    }
    
    qDebug() << "Whitelist saved to file, contains" << m_whiteList.size() << "entries";
    return true;
}

bool WhiteListManager::loadFromFile()
{
    QFile file(m_whiteListFile);
    if (!file.exists()) {
        qDebug() << "Whitelist file does not exist, will create new file:" << m_whiteListFile;
        return true;
    }
    
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Unable to open whitelist file for reading:" << m_whiteListFile;
        return false;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        qWarning() << "Failed to parse whitelist file:" << parseError.errorString();
        return false;
    }
    
    QJsonObject rootObject = doc.object();
    QJsonArray jsonArray = rootObject["whiteList"].toArray();
    
    QMutexLocker locker(&m_mutex);
    m_whiteList.clear();
    
    for (const QJsonValue &value : jsonArray) {
        QJsonObject entryObject = value.toObject();
        WhiteListEntry entry = WhiteListEntry::fromJson(entryObject);
        
        if (validateWhiteListEntry(entry)) {
            m_whiteList.append(entry);
        } else {
            qWarning() << "Skipping invalid whitelist entry:" << entry.getUniqueId();
        }
    }
    
    qDebug() << "Loaded whitelist from file, contains" << m_whiteList.size() << "entries";
    return true;
}

QString WhiteListManager::getWhiteListFilePath() const
{
    return m_whiteListFile;
}

QStringList WhiteListManager::findUSBKeys() const
{
    QStringList usbPaths;
    
    // 获取所有可移动存储设备
    QList<QStorageInfo> storageList = QStorageInfo::mountedVolumes();
    
    for (const QStorageInfo &storage : storageList) {
        if (storage.isValid() && storage.isReady()) {
            // 检查是否为可移动设备
            QString devicePath = storage.device();
            if (devicePath.startsWith("/dev/sd") || // Linux USB设备
                devicePath.contains("USB") ||       // Windows USB设备标识
                storage.fileSystemType() == "vfat" || // FAT32文件系统
                storage.fileSystemType() == "exfat") { // exFAT文件系统
                
                QString rootPath = storage.rootPath();
                
                // 检查是否包含白名单文件
                QString whiteListPath = QDir(rootPath).filePath(USB_WHITE_LIST_FILE_NAME);
                if (QFile::exists(whiteListPath)) {
                    usbPaths.append(rootPath);
                    qDebug() << "发现包含白名单的USB设备:" << rootPath;
                }
            }
        }
    }
    
    return usbPaths;
}

bool WhiteListManager::syncFromUSBKey(const QString &usbPath)
{
    QString usbWhiteListPath = QDir(usbPath).filePath(USB_WHITE_LIST_FILE_NAME);
    
    if (!QFile::exists(usbWhiteListPath)) {
        QString message = QString("USB设备中未找到白名单文件: %1").arg(USB_WHITE_LIST_FILE_NAME);
        qWarning() << message;
        emit syncCompleted(false, message);
        return false;
    }
    
    QList<WhiteListEntry> usbEntries;
    if (!parseUSBKeyData(usbWhiteListPath, usbEntries)) {
        QString message = "解析USB白名单文件失败";
        qWarning() << message;
        emit syncCompleted(false, message);
        return false;
    }
    
    QMutexLocker locker(&m_mutex);
    
    int addedCount = 0;
    int skippedCount = 0;
    
    for (const WhiteListEntry &usbEntry : usbEntries) {
        // 检查是否已存在
        bool exists = false;
        QString uniqueId = usbEntry.getUniqueId();
        
        for (const WhiteListEntry &existing : m_whiteList) {
            if (existing.getUniqueId() == uniqueId) {
                exists = true;
                skippedCount++;
                break;
            }
        }
        
        if (!exists && validateWhiteListEntry(usbEntry)) {
            WhiteListEntry newEntry = usbEntry;
            newEntry.source = "USB Key";
            newEntry.addTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
            
            m_whiteList.append(newEntry);
            addedCount++;
        }
    }
    
    m_lastSyncTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    
    bool saved = saveToFile();
    
    QString message = QString("同步完成：新增 %1 个，跳过 %2 个，总计 %3 个")
                     .arg(addedCount).arg(skippedCount).arg(m_whiteList.size());
    
    qDebug() << "USB白名单同步完成:" << message;
    
    if (saved && addedCount > 0) {
        emit whiteListUpdated(m_whiteList.size());
    }
    
    emit syncCompleted(saved, message);
    return saved;
}

bool WhiteListManager::parseUSBKeyData(const QString &filePath, QList<WhiteListEntry> &entries)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "无法打开USB白名单文件:" << filePath;
        return false;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        qWarning() << "解析USB白名单文件失败:" << parseError.errorString();
        return false;
    }
    
    QJsonObject rootObject = doc.object();
    QJsonArray jsonArray = rootObject["whiteList"].toArray();
    
    entries.clear();
    
    for (const QJsonValue &value : jsonArray) {
        QJsonObject entryObject = value.toObject();
        WhiteListEntry entry = WhiteListEntry::fromJson(entryObject);
        entries.append(entry);
    }
    
    qDebug() << "从USB设备解析到" << entries.size() << "个白名单条目";
    return true;
}

bool WhiteListManager::validateWhiteListEntry(const WhiteListEntry &entry) const
{
    return !entry.manufacturer.isEmpty() && 
           !entry.productCode.isEmpty() && 
           !entry.serialNumber.isEmpty();
}

int WhiteListManager::getLocalCount() const
{
    QMutexLocker locker(&m_mutex);
    int count = 0;
    for (const WhiteListEntry &entry : m_whiteList) {
        if (entry.source == "本地") {
            count++;
        }
    }
    return count;
}

int WhiteListManager::getUSBCount() const
{
    QMutexLocker locker(&m_mutex);
    int count = 0;
    for (const WhiteListEntry &entry : m_whiteList) {
        if (entry.source == "USB Key") {
            count++;
        }
    }
    return count;
}

QString WhiteListManager::getLastSyncTime() const
{
    return m_lastSyncTime;
}

void WhiteListManager::createBackup()
{
    if (!QFile::exists(m_whiteListFile)) {
        return;
    }
    
    QString backupFileName = generateBackupFileName();
    QString backupPath = QFileInfo(m_whiteListFile).absolutePath() + "/backup_" + backupFileName;
    
    if (QFile::copy(m_whiteListFile, backupPath)) {
        qDebug() << "创建白名单备份:" << backupPath;
    } else {
        qWarning() << "创建白名单备份失败";
    }
}

QString WhiteListManager::generateBackupFileName() const
{
    return QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + "_" + WHITE_LIST_FILE_NAME;
}

bool WhiteListManager::exportToUSBKey(const QString &usbPath)
{
    QString usbWhiteListPath = QDir(usbPath).filePath(USB_WHITE_LIST_FILE_NAME);

    // 创建导出数据
    QJsonArray jsonArray;

    QMutexLocker locker(&m_mutex);
    for (const WhiteListEntry &entry : m_whiteList) {
        jsonArray.append(entry.toJson());
    }

    QJsonObject rootObject;
    rootObject["version"] = "1.0";
    rootObject["exportTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    rootObject["count"] = m_whiteList.size();
    rootObject["whiteList"] = jsonArray;

    QJsonDocument doc(rootObject);

    QFile file(usbWhiteListPath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "无法创建USB白名单文件:" << usbWhiteListPath;
        return false;
    }

    qint64 bytesWritten = file.write(doc.toJson());
    file.close();

    if (bytesWritten == -1) {
        qWarning() << "写入USB白名单文件失败";
        return false;
    }

    qDebug() << "白名单已导出到USB设备:" << usbWhiteListPath;
    return true;
}
