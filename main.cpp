#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include "ServiceManager.h"
#include <QApplication>
#include <QDebug>
#include <QStringList>
#include <QMessageBox>
#include <QTextCodec>

#ifdef Q_OS_WIN
#include <windows.h>
#include <QCoreApplication>
#include <io.h>
#include <fcntl.h>
#endif

// Windows服务相关函数声明
#ifdef Q_OS_WIN
void WINAPI ServiceMain(DWORD argc, LPTSTR *argv);
void WINAPI ServiceCtrlHandler(DWORD ctrl);
void ReportServiceStatus(DWORD dwCurrentState, DWORD dwWin32ExitCode, DWORD dwWaitHint);
void ServiceWorkerThread();

// 全局变量
SERVICE_STATUS g_ServiceStatus = {0};
SERVICE_STATUS_HANDLE g_StatusHandle = nullptr;
HANDLE g_ServiceStopEvent = INVALID_HANDLE_VALUE;
#endif

// 设置中文编码支持函数
void setupChineseEncoding()
{
#ifdef Q_OS_WIN
    // 设置控制台代码页为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 设置控制台模式支持UTF-8
    HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hOut != INVALID_HANDLE_VALUE) {
        DWORD dwMode = 0;
        GetConsoleMode(hOut, &dwMode);
        dwMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
        SetConsoleMode(hOut, dwMode);
    }
#endif

    // 设置Qt的文本编码为UTF-8
    QTextCodec *utf8Codec = QTextCodec::codecForName("UTF-8");
    if (utf8Codec) {
        QTextCodec::setCodecForLocale(utf8Codec);
        qDebug() << "UTF-8 encoding setup completed";
    } else {
        qWarning() << "UTF-8 encoding setup failed";
    }
}

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // 首先设置中文编码支持
    setupChineseEncoding();

    // 设置应用程序信息
    a.setApplicationName("Monitor Control Program");
    a.setApplicationVersion("1.0");
    a.setOrganizationName("MonitorControl");

    // 解析命令行参数
    QStringList arguments = a.arguments();

    qDebug() << "========================================";
    qDebug() << "Monitor Control Program Starting";
    qDebug() << "Version: 1.0";
    qDebug() << "Build Time:" << __DATE__ << __TIME__;
    qDebug() << "Launch Arguments:" << arguments;
    qDebug() << "========================================";

    // 检查命令行参数
    if (arguments.contains("--service")) {
        qDebug() << "Starting in service mode...";
#ifdef Q_OS_WIN
        // Windows服务模式
        SERVICE_TABLE_ENTRY ServiceTable[] = {
            {const_cast<LPWSTR>(L"MonitorWhiteCtrlService"), (LPSERVICE_MAIN_FUNCTION)ServiceMain},
            {nullptr, nullptr}
        };

        if (StartServiceCtrlDispatcher(ServiceTable) == FALSE) {
            qWarning() << "Service control dispatcher startup failed:" << GetLastError();
            return 1;
        }
        return 0;
#else
        qWarning() << "Service mode not supported on this platform";
        return 1;
#endif
    }
    else if (arguments.contains("--startup")) {
        qDebug() << "Running in startup mode (hidden to tray)...";
        // 启动模式 - 直接隐藏到托盘，不显示主窗口
        CDlgMain_MonitorWhiteCtrlProgram w;
        // 完全隐藏主窗口，不显示在任务栏
        w.setWindowFlags(Qt::Tool | Qt::FramelessWindowHint);
        w.hide();
        return a.exec();
    }
    else if (arguments.contains("--admin-mode")) {
        qDebug() << "Running in admin mode (hidden to tray)...";
        // 管理员模式 - 也隐藏到托盘，不显示主窗口
        CDlgMain_MonitorWhiteCtrlProgram w;
        w.setWindowFlags(Qt::Tool | Qt::FramelessWindowHint);
        w.hide();
        return a.exec();
    }
    else {
        qDebug() << "Running in hidden mode (tray only)...";
        // 默认模式 - 隐藏主窗口，只显示托盘
        CDlgMain_MonitorWhiteCtrlProgram w;
        w.setWindowFlags(Qt::Tool | Qt::FramelessWindowHint);
        w.hide();
        return a.exec();
    }
}

#ifdef Q_OS_WIN
// Windows服务实现函数

void WINAPI ServiceMain(DWORD argc, LPTSTR *argv)
{
    Q_UNUSED(argc)
    Q_UNUSED(argv)

    qDebug() << "Service main function started";

    // 注册服务控制处理程序
    g_StatusHandle = RegisterServiceCtrlHandler(L"MonitorWhiteCtrlService", ServiceCtrlHandler);

    if (g_StatusHandle == nullptr) {
        qWarning() << "Failed to register service control handler:" << GetLastError();
        return;
    }

    // 初始化服务状态
    ZeroMemory(&g_ServiceStatus, sizeof(g_ServiceStatus));
    g_ServiceStatus.dwServiceType = SERVICE_WIN32_OWN_PROCESS;
    g_ServiceStatus.dwControlsAccepted = SERVICE_ACCEPT_STOP;
    g_ServiceStatus.dwCurrentState = SERVICE_START_PENDING;
    g_ServiceStatus.dwWin32ExitCode = 0;
    g_ServiceStatus.dwServiceSpecificExitCode = 0;
    g_ServiceStatus.dwCheckPoint = 0;

    // 报告服务正在启动
    ReportServiceStatus(SERVICE_START_PENDING, NO_ERROR, 3000);

    // 创建停止事件
    g_ServiceStopEvent = CreateEvent(nullptr, TRUE, FALSE, nullptr);
    if (g_ServiceStopEvent == nullptr) {
        qWarning() << "Failed to create stop event:" << GetLastError();
        ReportServiceStatus(SERVICE_STOPPED, GetLastError(), 0);
        return;
    }

    // 报告服务已启动
    ReportServiceStatus(SERVICE_RUNNING, NO_ERROR, 0);
    qDebug() << "Service is now running";

    // 启动工作线程
    ServiceWorkerThread();

    // 清理
    CloseHandle(g_ServiceStopEvent);
    ReportServiceStatus(SERVICE_STOPPED, NO_ERROR, 0);
    qDebug() << "Service main function ended";
}

void WINAPI ServiceCtrlHandler(DWORD ctrl)
{
    switch (ctrl) {
    case SERVICE_CONTROL_STOP:
        qDebug() << "Received service stop request";
        ReportServiceStatus(SERVICE_STOP_PENDING, NO_ERROR, 0);

        // 设置停止事件
        SetEvent(g_ServiceStopEvent);
        ReportServiceStatus(g_ServiceStatus.dwCurrentState, NO_ERROR, 0);
        break;

    default:
        break;
    }
}

void ReportServiceStatus(DWORD dwCurrentState, DWORD dwWin32ExitCode, DWORD dwWaitHint)
{
    static DWORD dwCheckPoint = 1;

    g_ServiceStatus.dwCurrentState = dwCurrentState;
    g_ServiceStatus.dwWin32ExitCode = dwWin32ExitCode;
    g_ServiceStatus.dwWaitHint = dwWaitHint;

    if (dwCurrentState == SERVICE_START_PENDING) {
        g_ServiceStatus.dwControlsAccepted = 0;
    } else {
        g_ServiceStatus.dwControlsAccepted = SERVICE_ACCEPT_STOP;
    }

    if ((dwCurrentState == SERVICE_RUNNING) || (dwCurrentState == SERVICE_STOPPED)) {
        g_ServiceStatus.dwCheckPoint = 0;
    } else {
        g_ServiceStatus.dwCheckPoint = dwCheckPoint++;
    }

    SetServiceStatus(g_StatusHandle, &g_ServiceStatus);
}

void ServiceWorkerThread()
{
    qDebug() << "Service worker thread started";

    // 在服务模式下运行核心功能
    // 注意：在服务模式下不能显示GUI，只能运行后台逻辑

    // 创建核心管理器（不包含GUI相关的部分）
    // 这里我们需要创建一个无GUI版本的监控逻辑

    // 等待停止信号
    while (WaitForSingleObject(g_ServiceStopEvent, 1000) != WAIT_OBJECT_0) {
        // 每秒检查一次停止信号
        // 在这里可以执行定期的监控任务

        // 检查服务是否应该停止
        if (g_ServiceStatus.dwCurrentState == SERVICE_STOP_PENDING) {
            break;
        }
    }

    qDebug() << "Service worker thread ended";
}

#endif
