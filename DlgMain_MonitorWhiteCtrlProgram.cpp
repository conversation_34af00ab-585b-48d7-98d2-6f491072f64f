#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include "ui_DlgMain_MonitorWhiteCtrlProgram.h"
#include "TrayManager.h"
#include "WhiteListManager.h"
#include "USBDeviceMonitor.h"
#include "ServiceManager.h"
#include <QApplication>
#include <QMessageBox>
#include <QInputDialog>
#include <QMutexLocker>
#include <QFileInfo>
#include <QDir>
#include <QCoreApplication>

// Windows显示器枚举回调函数
#ifdef Q_OS_WIN
BOOL CALLBACK MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData)
{
    Q_UNUSED(hdcMonitor)

    MONITORINFOEX monitorInfo;
    monitorInfo.cbSize = sizeof(MONITORINFOEX);

    // 获取显示器详细信息
    if (GetMonitorInfo(hMonitor, &monitorInfo)) {
        QString deviceName = QString::fromWCharArray(monitorInfo.szDevice);

        qDebug() << "Monitor device:" << deviceName;
        qDebug() << "  Position: (" << lprcMonitor->left << ", " << lprcMonitor->top << ")";
        qDebug() << "  Size:" << (lprcMonitor->right - lprcMonitor->left) << " x "
                  << (lprcMonitor->bottom - lprcMonitor->top) << " pixels";
        qDebug() << "  Is primary:" << (monitorInfo.dwFlags & MONITORINFOF_PRIMARY ? "Yes" : "No");

        // 将显示器信息传递给调用者
        if (dwData) {
            QList<DisplayInfo> *displaysList = reinterpret_cast<QList<DisplayInfo>*>(dwData);

            // 查找对应的EDID信息
            for (DisplayInfo &info : *displaysList) {
                if (info.deviceName.contains(deviceName) || deviceName.contains(info.deviceName)) {
                    info.isPrimary = (monitorInfo.dwFlags & MONITORINFOF_PRIMARY) != 0;
                    break;
                }
            }
        }
    }
    return TRUE; // 返回 TRUE 继续枚举，返回 FALSE 停止枚举
}
#endif

// ===============================
// 主窗口类实现
// ===============================

CDlgMain_MonitorWhiteCtrlProgram::CDlgMain_MonitorWhiteCtrlProgram(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::CDlgMain_MonitorWhiteCtrlProgram),
    m_edidManager(nullptr),
    m_trayManager(nullptr),
    m_whiteListManager(nullptr),
    m_usbDeviceMonitor(nullptr),
    m_serviceManager(nullptr),
    m_isInitialized(false)
{
    ui->setupUi(this);

    // 设置窗口为隐藏模式，不显示在任务栏
    setWindowFlags(Qt::Tool | Qt::FramelessWindowHint);

    // 设置窗口标题
    setWindowTitle("显示器管控终端程序 v1.0");

    // 确保窗口隐藏
    hide();

    // 记录程序启动
    WriteLog("显示器管控终端程序启动");
    //WriteLog("测试中文编码：这是一条包含中文的测试日志");
    //WriteLog("特殊字符测试：①②③④⑤ ★☆♠♥♦♣ αβγδε");

    // 按顺序初始化所有管理器
    initEDIDManager();
    initWhiteListManager();
    initUSBDeviceMonitor();
    initServiceManager();
    initTrayManager();

    // 显示所有显示器信息
    showDisplaysInfo();

    // 更新窗口标题
    updateWindowTitle();

    m_isInitialized = true;

    qDebug() << "Main window initialization completed";
}

CDlgMain_MonitorWhiteCtrlProgram::~CDlgMain_MonitorWhiteCtrlProgram()
{
    WriteLog("显示器管控终端程序退出");

    // 按相反顺序清理管理器
    if (m_trayManager) {
        delete m_trayManager;
        m_trayManager = nullptr;
    }

    if (m_usbDeviceMonitor) {
        delete m_usbDeviceMonitor;
        m_usbDeviceMonitor = nullptr;
    }

    if (m_whiteListManager) {
        delete m_whiteListManager;
        m_whiteListManager = nullptr;
    }

    if (m_serviceManager) {
        delete m_serviceManager;
        m_serviceManager = nullptr;
    }

    if (m_edidManager) {
        delete m_edidManager;
        m_edidManager = nullptr;
    }

    delete ui;
}

void CDlgMain_MonitorWhiteCtrlProgram::initEDIDManager()
{
    m_edidManager = new EDIDManager(this);

    // 连接信号槽
    connect(m_edidManager, &EDIDManager::displaysChanged,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onDisplaysChanged);
}

void CDlgMain_MonitorWhiteCtrlProgram::refreshDisplaysInfo()
{
    if (m_edidManager) {
        m_edidManager->refreshDisplaysInfo();
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::showDisplaysInfo()
{
    if (!m_edidManager) return;

    QList<DisplayInfo> displays = m_edidManager->getAllDisplaysInfo();

    qDebug() << "\n========== Monitor EDID Information ==========";
    qDebug() << "Detected" << displays.size() << "monitors:";

    for (int i = 0; i < displays.size(); ++i) {
        const DisplayInfo &info = displays[i];

        qDebug() << "\n--- Monitor" << (i + 1) << "---";
        qDebug() << "Device Name:" << info.deviceName;
        qDebug() << "Manufacturer:" << info.manufacturer;
        qDebug() << "Product Code:" << info.productCode;
        qDebug() << "Serial Number:" << info.serialNumber;
        qDebug() << "Manufacture Date:" << "Week" << info.manufactureWeek << "," << info.manufactureYear;
        qDebug() << "EDID Version:" << info.edidVersion;
        qDebug() << "Physical Size:" << info.physicalSize.width() << "x" << info.physicalSize.height() << "mm";
        qDebug() << "Native Resolution:" << info.nativeResolution.width() << "x" << info.nativeResolution.height();
        qDebug() << "Is Primary:" << (info.isPrimary ? "Yes" : "No");

        qDebug() << "Supported Resolutions:";
        for (const QSize &res : info.supportedResolutions) {
            qDebug() << "  " << res.width() << "x" << res.height();
        }

        qDebug() << "Raw EDID Data (first 32 bytes):" << info.rawEDID.left(32).toHex();

        // 记录日志
        WriteLog(QString("检测到显示器: %1 (%2 %3)")
                .arg(info.deviceName)
                .arg(info.manufacturer)
                .arg(info.productCode));
    }

    qDebug() << "=============================================\n";
}

void CDlgMain_MonitorWhiteCtrlProgram::onDisplaysChanged()
{
    qDebug() << "Monitor configuration changed, reloading information...";
    showDisplaysInfo();

    WriteLog("显示器配置发生变化");
}

void CDlgMain_MonitorWhiteCtrlProgram::WriteLog(const QString &message)
{
    QMutexLocker locker(&m_mutex_Log);  // 使用RAII方式管理锁

    if (!m_file_Log.isOpen())
    {
        // 获取程序所在目录
        QString appDirPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath();
        QString logDirPath = QDir(appDirPath).filePath("Log");

        // 检查Log文件夹是否存在，不存在则创建
        QDir logDir(logDirPath);
        if (!logDir.exists()) {
            if (logDir.mkpath(logDirPath)) {
                qDebug() << "Log directory created:" << logDirPath;
            } else {
                qWarning() << "Failed to create log directory:" << logDirPath;
                // 如果创建失败，使用程序目录作为备选
                logDirPath = appDirPath;
            }
        } else {
            qDebug() << "Log directory exists:" << logDirPath;
        }

        // 生成日志文件名（包含时间戳以避免冲突）
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd");
        QString logFileName = QString("%1_MonitorCtrl.log").arg(timestamp);
        QString logFilePath = QDir(logDirPath).filePath(logFileName);

        // 设置日志文件路径并打开
        m_file_Log.setFileName(logFilePath);
        if (m_file_Log.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
            m_textStream_Out.setDevice(&m_file_Log);

            // 写入日志文件头信息
            m_textStream_Out << "========================================\n";
            m_textStream_Out << "Monitor Control Program Log File\n";
            m_textStream_Out << "Start Time: " << QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz") << "\n";
            m_textStream_Out << "Program Version: v1.0\n";
            m_textStream_Out << "User Name: " << getSystemUserName() << "\n";
            m_textStream_Out << "Log File: " << logFilePath << "\n";
            m_textStream_Out << "========================================\n";
            m_textStream_Out.flush();

            qDebug() << "Log file created successfully:" << logFilePath;
        } else {
            qWarning() << "Failed to create log file:" << logFilePath;
            qWarning() << "Error:" << m_file_Log.errorString();
        }
    }

    // 写入日志消息
    if (m_file_Log.isOpen()) {
        QString logEntry = QString("[%1] %2")
                          .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz"))
                          .arg(message);

        // 只写入日志文件，不输出到控制台
        m_textStream_Out << logEntry << "\n";
        m_textStream_Out.flush(); // 确保立即写入磁盘
    }
}

QString CDlgMain_MonitorWhiteCtrlProgram::getSystemUserName() {
//    // 方法一：通过环境变量
//    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
//    QString userName = env.value("USERNAME");  // Windows
//    if (userName.isEmpty()) {
//        userName = env.value("USER");  // Linux/macOS
//    }

//    if (!userName.isEmpty()) {
//        return userName;
//    }

    // 方法二：通过平台 API
#ifdef Q_OS_WIN
    #ifndef UNLEN
    #define UNLEN 256
    #endif
    wchar_t username[UNLEN + 1];
    DWORD size = UNLEN + 1;
    if (GetUserNameW(username, &size)) {
        return QString::fromWCharArray(username);
    }
#else
    char* username = getlogin();
    if (username) {
        return QString::fromLocal8Bit(username);
    }
#endif

    return QString("Unknown");
}

// ===============================
// 新增管理器初始化函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::initTrayManager()
{
    m_trayManager = new TrayManager(this, this);

    // 连接托盘信号到主窗口槽函数
    connect(m_trayManager, &TrayManager::clearWhiteListRequested,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onClearWhiteList);
    connect(m_trayManager, &TrayManager::syncWhiteListRequested,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onSyncWhiteList);
    connect(m_trayManager, &TrayManager::startServiceRequested,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onStartServiceFromTray);
    connect(m_trayManager, &TrayManager::uninstallServiceRequested,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onUninstallServiceFromTray);
    connect(m_trayManager, &TrayManager::deleteServiceRequested,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onDeleteServiceFromTray);

    // 连接白名单管理器到托盘管理器（如果白名单管理器已初始化）
    if (m_whiteListManager) {
        connect(m_whiteListManager, &WhiteListManager::whiteListUpdated,
                m_trayManager, &TrayManager::onWhiteListUpdated);
    }

    // 初始化服务菜单状态（如果服务管理器已初始化）
    if (m_serviceManager) {
        bool serviceInstalled = m_serviceManager->isServiceInstalled();
        bool serviceRunning = m_serviceManager->isServiceRunning();
        m_trayManager->updateServiceMenuStatus(serviceInstalled, serviceRunning);
    }

    qDebug() << "TrayManager initialized";
}

void CDlgMain_MonitorWhiteCtrlProgram::initWhiteListManager()
{
    m_whiteListManager = new WhiteListManager(this);

    // 连接白名单信号
    connect(m_whiteListManager, &WhiteListManager::whiteListUpdated,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onWhiteListUpdated);
    connect(m_whiteListManager, &WhiteListManager::syncCompleted,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onSyncCompleted);

    qDebug() << "WhiteListManager initialized";
}

void CDlgMain_MonitorWhiteCtrlProgram::initUSBDeviceMonitor()
{
    m_usbDeviceMonitor = new USBDeviceMonitor(m_whiteListManager, this);

    // 连接USB设备信号
    connect(m_usbDeviceMonitor, &USBDeviceMonitor::whiteListUSBKeyDetected,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onUSBKeyDetected);

    // 启动USB设备监控
    m_usbDeviceMonitor->startMonitoring();

    qDebug() << "USBDeviceMonitor initialized";
}

void CDlgMain_MonitorWhiteCtrlProgram::initServiceManager()
{
    m_serviceManager = new ServiceManager(this);

    qDebug() << "ServiceManager initialized";
    qDebug() << "Service status:" << m_serviceManager->getServiceStatusString();
    qDebug() << "Auto start:" << (m_serviceManager->isAutoStartEnabled() ? "Enabled" : "Disabled");

    // 软件启动时自动启动服务
    autoStartServiceOnStartup();
}

void CDlgMain_MonitorWhiteCtrlProgram::autoStartServiceOnStartup()
{
    if (!m_serviceManager) {
        qWarning() << "ServiceManager not initialized";
        return;
    }

    qDebug() << "Checking service auto-start on software startup...";

    // 检查服务是否已安装
    if (!m_serviceManager->isServiceInstalled()) {
        qDebug() << "Service not installed, attempting to install...";

        // 检查是否有管理员权限
        if (!m_serviceManager->isRunningAsAdmin()) {
            qDebug() << "No admin privileges, skipping service installation";
            WriteLog("Service installation skipped: No admin privileges");
            return;
        }

        // 尝试安装服务
        if (m_serviceManager->installService()) {
            qDebug() << "Service installed successfully";
            WriteLog("Service installed successfully");
        } else {
            qWarning() << "Failed to install service";
            WriteLog("Failed to install service");
            return;
        }
    }

    // 检查服务是否正在运行
    if (m_serviceManager->isServiceRunning()) {
        qDebug() << "Service is already running";
        WriteLog("Service is already running");
        return;
    }

    // 尝试启动服务
    qDebug() << "Attempting to start service...";
    WriteLog("Attempting to start service on software startup");

    if (m_serviceManager->startService()) {
        qDebug() << "Service started successfully";
        WriteLog("Service started successfully");

        // 如果还没有启用自启动，自动启用
        if (!m_serviceManager->isAutoStartEnabled()) {
            qDebug() << "Auto-start not enabled, enabling auto-start...";
            WriteLog("Auto-start not enabled, enabling auto-start...");

            if (m_serviceManager->setAutoStart(true)) {
                qDebug() << "Auto-start enabled successfully";
                WriteLog("Auto-start enabled successfully");
            } else {
                qWarning() << "Failed to enable auto-start";
                WriteLog("Failed to enable auto-start");
            }
        } else {
            qDebug() << "Auto-start already enabled";
            WriteLog("Auto-start already enabled");
        }
    } else {
        qWarning() << "Failed to start service";
        WriteLog("Failed to start service");
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::updateWindowTitle()
{
    QString title = "显示器管控终端程序 v1.0";

    if (m_whiteListManager) {
        int whiteListCount = m_whiteListManager->getWhiteListCount();
        title += QString(" - 白名单: %1 个显示器").arg(whiteListCount);
    }

    if (m_edidManager) {
        QList<DisplayInfo> displays = m_edidManager->getAllDisplaysInfo();
        title += QString(" - 当前: %1 个显示器").arg(displays.size());
    }

    setWindowTitle(title);
}

// ===============================
// 窗口事件处理
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::closeEvent(QCloseEvent *event)
{
    // 始终隐藏窗口而不是退出程序
    hide();
    if (m_trayManager) {
        m_trayManager->updateTrayStatus("后台运行");
    }
    event->ignore();

    WriteLog("主窗口关闭事件被忽略，程序继续后台运行");
}

void CDlgMain_MonitorWhiteCtrlProgram::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::WindowStateChange) {
        if (isMinimized()) {
            // 最小化时始终隐藏窗口
            hide();
            event->ignore();
            WriteLog("窗口最小化，自动隐藏");
            return;
        }
    }

    QMainWindow::changeEvent(event);
}

// ===============================
// 托盘相关槽函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::onClearWhiteList()
{
    if (!m_whiteListManager) {
        return;
    }

    int currentCount = m_whiteListManager->getWhiteListCount();
    if (currentCount == 0) {
        QMessageBox::information(this, "白名单管理", "白名单已经为空。");
        return;
    }

    m_whiteListManager->clearWhiteList();

    WriteLog(QString("用户清空白名单，原有 %1 个条目").arg(currentCount));
    updateWindowTitle();

    qDebug() << "WhiteList cleared";
}

void CDlgMain_MonitorWhiteCtrlProgram::onSyncWhiteList()
{
    if (!m_whiteListManager) {
        return;
    }

    // 查找USB Key
    QStringList usbKeys = m_whiteListManager->findUSBKeys();

    if (usbKeys.isEmpty()) {
        QMessageBox::information(this, "白名单同步",
                                "未找到包含白名单文件的USB设备。\n\n"
                                "请确保USB设备中包含 monitor_whitelist.json 文件。");
        return;
    }

    if (usbKeys.size() == 1) {
        // 只有一个USB Key，直接同步
        QString usbPath = usbKeys.first();
        WriteLog(QString("开始同步USB Key白名单: %1").arg(usbPath));
        m_whiteListManager->syncFromUSBKey(usbPath);
    } else {
        // 多个USB Key，让用户选择
        QStringList items;
        for (const QString &usbPath : usbKeys) {
            items << QString("USB设备: %1").arg(usbPath);
        }

        bool ok;
        QString selectedItem = QInputDialog::getItem(this, "选择USB设备",
                                                    "发现多个包含白名单的USB设备，请选择：",
                                                    items, 0, false, &ok);
        if (ok && !selectedItem.isEmpty()) {
            int index = items.indexOf(selectedItem);
            if (index >= 0 && index < usbKeys.size()) {
                QString usbPath = usbKeys[index];
                WriteLog(QString("用户选择同步USB Key白名单: %1").arg(usbPath));
                m_whiteListManager->syncFromUSBKey(usbPath);
            }
        }
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::showMainWindow()
{
    show();
    raise();
    activateWindow();

    if (m_trayManager) {
        m_trayManager->updateTrayStatus("运行中");
    }

    WriteLog("主窗口显示");
}

void CDlgMain_MonitorWhiteCtrlProgram::hideToTray()
{
    if (m_trayManager && m_trayManager->isSystemTrayAvailable()) {
        hide();
        if (m_trayManager) {
            m_trayManager->updateTrayStatus("后台运行");
        }
        WriteLog("主窗口隐藏到托盘");
    }
}

// ===============================
// 其他槽函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::onWhiteListUpdated(int count)
{
    updateWindowTitle();
    WriteLog(QString("白名单已更新，当前包含 %1 个显示器").arg(count));

    qDebug() << "WhiteList updated, count:" << count;
}

void CDlgMain_MonitorWhiteCtrlProgram::onUSBKeyDetected(const QString &usbPath)
{
    WriteLog(QString("检测到包含白名单的USB Key: %1").arg(usbPath));

    // 可以在这里添加自动同步的逻辑
    // 或者显示通知让用户确认是否同步

    qDebug() << "WhiteList USB Key detected:" << usbPath;
}

void CDlgMain_MonitorWhiteCtrlProgram::onSyncCompleted(bool success, const QString &message)
{
    if (success) {
        QMessageBox::information(this, "同步完成", message);
        WriteLog(QString("白名单同步成功: %1").arg(message));
    } else {
        QMessageBox::warning(this, "同步失败", message);
        WriteLog(QString("白名单同步失败: %1").arg(message));
    }

    updateWindowTitle();
}

// ===============================
// 托盘服务管理槽函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::onStartServiceFromTray()
{
    qDebug() << "Starting service from tray menu";
    WriteLog("User requested service start from tray");

    // 检查管理员权限
    if (!m_serviceManager->isRunningAsAdmin()) {
        if (m_trayManager) {
            m_trayManager->showMessage("权限不足",
                                     "开启服务需要管理员权限。\n请以管理员身份运行程序。",
                                     QSystemTrayIcon::Warning);
        }
        WriteLog("Service start failed: No admin privileges");
        return;
    }

    // 检查服务状态
    bool serviceInstalled = m_serviceManager->isServiceInstalled();
    bool serviceRunning = m_serviceManager->isServiceRunning();

    if (serviceRunning) {
        if (m_trayManager) {
            m_trayManager->showMessage("服务状态",
                                     "服务已经在运行中。",
                                     QSystemTrayIcon::Information);
        }
        WriteLog("Service is already running");
        return;
    }

    // 执行服务启动流程
    bool success = false;

    if (!serviceInstalled) {
        qDebug() << "Installing service...";
        WriteLog("Installing service from tray...");

        if (m_serviceManager->installService()) {
            qDebug() << "Service installed successfully";
            WriteLog("Service installed successfully");
            success = true;
        } else {
            qWarning() << "Failed to install service";
            WriteLog("Failed to install service");
        }
    } else {
        success = true;
    }

    if (success) {
        qDebug() << "Starting service...";
        WriteLog("Starting service from tray...");

        if (m_serviceManager->startService()) {
            qDebug() << "Service started successfully";
            WriteLog("Service started successfully");

            // 启用自启动
            if (!m_serviceManager->isAutoStartEnabled()) {
                if (m_serviceManager->setAutoStart(true)) {
                    qDebug() << "Auto-start enabled successfully";
                    WriteLog("Auto-start enabled successfully");
                } else {
                    qWarning() << "Failed to enable auto-start";
                    WriteLog("Failed to enable auto-start");
                }
            }

            if (m_trayManager) {
                m_trayManager->showMessage("服务管理",
                                         "服务开启成功！\n\n"
                                         "• Windows服务已启动\n"
                                         "• 开机自启动已启用",
                                         QSystemTrayIcon::Information);
            }

            // 更新窗口标题
            updateWindowTitle();

            // 更新托盘菜单状态
            if (m_trayManager) {
                bool serviceInstalled = m_serviceManager->isServiceInstalled();
                bool serviceRunning = m_serviceManager->isServiceRunning();
                m_trayManager->updateServiceMenuStatus(serviceInstalled, serviceRunning);
            }
        } else {
            qWarning() << "Failed to start service";
            WriteLog("Failed to start service");

            if (m_trayManager) {
                m_trayManager->showMessage("服务管理",
                                         "服务启动失败！\n请检查系统权限和服务状态。",
                                         QSystemTrayIcon::Critical);
            }
        }
    } else {
        if (m_trayManager) {
            m_trayManager->showMessage("服务管理",
                                     "服务安装失败！\n请检查管理员权限。",
                                     QSystemTrayIcon::Critical);
        }
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::onUninstallServiceFromTray()
{
    qDebug() << "Uninstalling service from tray menu";
    WriteLog("User requested service uninstall from tray");

    // 检查管理员权限
    if (!m_serviceManager->isRunningAsAdmin()) {
        if (m_trayManager) {
            m_trayManager->showMessage("权限不足",
                                     "卸载服务需要管理员权限。\n请以管理员身份运行程序。",
                                     QSystemTrayIcon::Warning);
        }
        WriteLog("Service uninstall failed: No admin privileges");
        return;
    }

    // 检查服务状态
    bool serviceInstalled = m_serviceManager->isServiceInstalled();
    bool autoStartEnabled = m_serviceManager->isAutoStartEnabled();

    if (!serviceInstalled && !autoStartEnabled) {
        if (m_trayManager) {
            m_trayManager->showMessage("服务状态",
                                     "服务未安装或已经卸载。",
                                     QSystemTrayIcon::Information);
        }
        WriteLog("Service is not installed");
        return;
    }

    // 执行完整卸载
    qDebug() << "Starting complete service uninstallation from tray...";
    WriteLog("Starting complete service uninstallation from tray...");

    if (m_serviceManager->completeUninstall()) {
        qDebug() << "Service uninstalled successfully from tray";
        WriteLog("Service uninstalled successfully from tray");

        if (m_trayManager) {
            m_trayManager->showMessage("服务管理",
                                     "服务卸载成功！\n\n"
                                     "• Windows服务已删除\n"
                                     "• 开机自启动项已删除\n"
                                     "• 相关配置已清理",
                                     QSystemTrayIcon::Information);
        }

        // 更新窗口标题
        updateWindowTitle();

        // 更新托盘菜单状态
        if (m_trayManager) {
            bool serviceInstalled = m_serviceManager->isServiceInstalled();
            bool serviceRunning = m_serviceManager->isServiceRunning();
            m_trayManager->updateServiceMenuStatus(serviceInstalled, serviceRunning);
        }
    } else {
        qWarning() << "Failed to uninstall service from tray";
        WriteLog("Failed to uninstall service from tray");

        if (m_trayManager) {
            m_trayManager->showMessage("服务管理",
                                     "服务卸载失败！\n\n"
                                     "请检查：\n"
                                     "• 管理员权限\n"
                                     "• 服务使用状态\n"
                                     "• 系统安全设置",
                                     QSystemTrayIcon::Critical);
        }
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::onDeleteServiceFromTray()
{
    qDebug() << "Force deleting service from tray menu";
    WriteLog("User requested service force deletion from tray");

    // 检查管理员权限
    if (!m_serviceManager->isRunningAsAdmin()) {
        if (m_trayManager) {
            m_trayManager->showMessage("权限不足",
                                     "删除服务需要管理员权限。\n请以管理员身份运行程序。",
                                     QSystemTrayIcon::Warning);
        }
        WriteLog("Service force deletion failed: No admin privileges");
        return;
    }

    // 检查服务状态
    bool serviceInstalled = m_serviceManager->isServiceInstalled();
    bool autoStartEnabled = m_serviceManager->isAutoStartEnabled();

    if (!serviceInstalled && !autoStartEnabled) {
        if (m_trayManager) {
            m_trayManager->showMessage("服务状态",
                                     "服务未安装或已经删除。",
                                     QSystemTrayIcon::Information);
        }
        WriteLog("Service is not installed");
        return;
    }

    // 执行强制删除
    qDebug() << "Starting force service deletion from tray...";
    WriteLog("Starting force service deletion from tray...");

    if (m_serviceManager->forceDeleteService()) {
        qDebug() << "Service force deleted successfully from tray";
        WriteLog("Service force deleted successfully from tray");

        if (m_trayManager) {
            m_trayManager->showMessage("服务管理",
                                     "服务强制删除成功！\n\n"
                                     "• 服务进程已终止\n"
                                     "• Windows服务已删除\n"
                                     "• 注册表项已清理\n"
                                     "• 启动项已删除\n"
                                     "• 临时文件已清理",
                                     QSystemTrayIcon::Information);
        }

        // 更新窗口标题
        updateWindowTitle();

        // 更新托盘菜单状态
        if (m_trayManager) {
            bool serviceInstalled = m_serviceManager->isServiceInstalled();
            bool serviceRunning = m_serviceManager->isServiceRunning();
            m_trayManager->updateServiceMenuStatus(serviceInstalled, serviceRunning);
        }
    } else {
        qWarning() << "Failed to force delete service from tray";
        WriteLog("Failed to force delete service from tray");

        if (m_trayManager) {
            m_trayManager->showMessage("服务管理",
                                     "服务强制删除失败！\n\n"
                                     "可能的原因：\n"
                                     "• 管理员权限不足\n"
                                     "• 服务正在被其他程序使用\n"
                                     "• 系统安全策略阻止\n"
                                     "• 注册表访问受限\n\n"
                                     "建议：重启系统后重试",
                                     QSystemTrayIcon::Critical);
        }
    }
}
