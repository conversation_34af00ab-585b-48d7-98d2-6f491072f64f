#include "TrayManager.h"
#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include <QIcon>
#include <QPixmap>
#include <QPainter>
#include <QFont>

TrayManager::TrayManager(CDlgMain_MonitorWhiteCtrlProgram *mainWindow, QObject *parent)
    : QObject(parent)
    , m_mainWindow(mainWindow)
    , m_trayIcon(nullptr)
    , m_trayMenu(nullptr)
    , m_currentStatus("就绪")
    , m_whiteListCount(0)
{
    if (isSystemTrayAvailable()) {
        createTrayIcon();
        createTrayMenu();
        showTrayIcon();
        
        qDebug() << "System tray initialized";
    } else {
        qWarning() << "System tray not available";
    }
}

TrayManager::~TrayManager()
{
    if (m_trayIcon) {
        m_trayIcon->hide();
        delete m_trayIcon;
    }
    
    if (m_trayMenu) {
        delete m_trayMenu;
    }
}

bool TrayManager::isSystemTrayAvailable()
{
    return QSystemTrayIcon::isSystemTrayAvailable();
}

void TrayManager::createTrayIcon()
{
    m_trayIcon = new QSystemTrayIcon(this);
    
    // 创建托盘图标
    QPixmap pixmap(32, 32);
    pixmap.fill(Qt::transparent);
    
    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制显示器图标
    painter.setBrush(QBrush(QColor(70, 130, 180)));
    painter.setPen(QPen(QColor(25, 25, 112), 2));
    painter.drawRoundedRect(4, 6, 24, 16, 2, 2);
    
    // 绘制显示器底座
    painter.drawRect(14, 22, 4, 4);
    painter.drawRect(10, 26, 12, 2);
    
    // 绘制状态指示点
    painter.setBrush(QBrush(QColor(0, 255, 0)));
    painter.setPen(Qt::NoPen);
    painter.drawEllipse(26, 8, 4, 4);
    
    QIcon icon(pixmap);
    m_trayIcon->setIcon(icon);
    
    // 设置工具提示
    updateTrayStatus(m_currentStatus);
    
    // 连接信号
    connect(m_trayIcon, &QSystemTrayIcon::activated,
            this, &TrayManager::onTrayIconActivated);
}

void TrayManager::createTrayMenu()
{
    m_trayMenu = new QMenu();
    
    // 创建菜单项
    m_showAction = new QAction("显示主窗口", this);
    m_showAction->setIcon(QIcon(":/icons/show.png"));
    connect(m_showAction, &QAction::triggered, this, &TrayManager::onShowMainWindow);
    
    m_hideAction = new QAction("隐藏主窗口", this);
    m_hideAction->setIcon(QIcon(":/icons/hide.png"));
    connect(m_hideAction, &QAction::triggered, this, &TrayManager::onHideMainWindow);
    
    m_clearWhiteListAction = new QAction("清空白名单", this);
    m_clearWhiteListAction->setIcon(QIcon(":/icons/clear.png"));
    connect(m_clearWhiteListAction, &QAction::triggered, this, &TrayManager::onClearWhiteList);
    
    m_syncWhiteListAction = new QAction("同步白名单", this);
    m_syncWhiteListAction->setIcon(QIcon(":/icons/sync.png"));
    connect(m_syncWhiteListAction, &QAction::triggered, this, &TrayManager::onSyncWhiteList);

    m_startServiceAction = new QAction("开启服务", this);
    m_startServiceAction->setIcon(QIcon(":/icons/service.png"));
    connect(m_startServiceAction, &QAction::triggered, this, &TrayManager::onStartService);

    m_uninstallServiceAction = new QAction("卸载服务", this);
    m_uninstallServiceAction->setIcon(QIcon(":/icons/uninstall.png"));
    connect(m_uninstallServiceAction, &QAction::triggered, this, &TrayManager::onUninstallService);

    m_deleteServiceAction = new QAction("删除服务", this);
    m_deleteServiceAction->setIcon(QIcon(":/icons/delete.png"));
    connect(m_deleteServiceAction, &QAction::triggered, this, &TrayManager::onDeleteService);

    m_aboutAction = new QAction("关于", this);
    m_aboutAction->setIcon(QIcon(":/icons/about.png"));
    connect(m_aboutAction, &QAction::triggered, this, &TrayManager::onAbout);
    
    m_exitAction = new QAction("退出", this);
    m_exitAction->setIcon(QIcon(":/icons/exit.png"));
    connect(m_exitAction, &QAction::triggered, this, &TrayManager::onExit);
    
    // 添加菜单项 (隐藏窗口管理和退出菜单)
    // m_trayMenu->addAction(m_showAction);        // 隐藏"显示主窗口"
    // m_trayMenu->addAction(m_hideAction);        // 隐藏"隐藏主窗口"
    // m_trayMenu->addSeparator();
    m_trayMenu->addAction(m_clearWhiteListAction);
    m_trayMenu->addAction(m_syncWhiteListAction);
    m_trayMenu->addSeparator();
    m_trayMenu->addAction(m_startServiceAction);
    m_trayMenu->addAction(m_uninstallServiceAction);
    m_trayMenu->addAction(m_deleteServiceAction);
    m_trayMenu->addSeparator();
    m_trayMenu->addAction(m_aboutAction);
    // m_trayMenu->addSeparator();
    // m_trayMenu->addAction(m_exitAction);        // 隐藏"退出"
    
    // 设置托盘菜单
    m_trayIcon->setContextMenu(m_trayMenu);
}

void TrayManager::showTrayIcon()
{
    if (m_trayIcon && isSystemTrayAvailable()) {
        m_trayIcon->show();
        qDebug() << "Tray icon displayed";
    }
}

void TrayManager::hideTrayIcon()
{
    if (m_trayIcon) {
        m_trayIcon->hide();
        qDebug() << "Tray icon hidden";
    }
}

void TrayManager::updateTrayStatus(const QString &status)
{
    m_currentStatus = status;

    if (m_trayIcon) {
        QString tooltip = QString("显示器管控终端程序\n状态: %1\n白名单: %2 个显示器")
                         .arg(status)
                         .arg(m_whiteListCount);
        m_trayIcon->setToolTip(tooltip);
    }
}

void TrayManager::updateServiceMenuStatus(bool serviceInstalled, bool serviceRunning)
{
    if (!m_startServiceAction || !m_uninstallServiceAction || !m_deleteServiceAction) {
        return;
    }

    // 更新"开启服务"菜单项
    if (serviceRunning) {
        m_startServiceAction->setText("服务已运行");
        m_startServiceAction->setEnabled(false);
        m_startServiceAction->setToolTip("服务当前正在运行中");
    } else if (serviceInstalled) {
        m_startServiceAction->setText("启动服务");
        m_startServiceAction->setEnabled(true);
        m_startServiceAction->setToolTip("启动已安装的服务");
    } else {
        m_startServiceAction->setText("安装并启动服务");
        m_startServiceAction->setEnabled(true);
        m_startServiceAction->setToolTip("安装并启动Windows服务");
    }

    // 更新"卸载服务"菜单项
    if (serviceInstalled || serviceRunning) {
        m_uninstallServiceAction->setText("卸载服务");
        m_uninstallServiceAction->setEnabled(true);
        m_uninstallServiceAction->setToolTip("完全卸载Windows服务和自启动项");
    } else {
        m_uninstallServiceAction->setText("服务未安装");
        m_uninstallServiceAction->setEnabled(false);
        m_uninstallServiceAction->setToolTip("服务未安装，无需卸载");
    }

    // 更新"删除服务"菜单项
    if (serviceInstalled || serviceRunning) {
        m_deleteServiceAction->setText("删除服务");
        m_deleteServiceAction->setEnabled(true);
        m_deleteServiceAction->setToolTip("强制删除Windows服务（包括注册表清理）");
    } else {
        m_deleteServiceAction->setText("服务未安装");
        m_deleteServiceAction->setEnabled(false);
        m_deleteServiceAction->setToolTip("服务未安装，无需删除");
    }

    qDebug() << "Service menu status updated - Installed:" << serviceInstalled << "Running:" << serviceRunning;
}

void TrayManager::onTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    switch (reason) {
    case QSystemTrayIcon::DoubleClick:
        // 双击不执行任何操作（禁用显示主窗口）
        qDebug() << "Tray icon double-clicked (action disabled)";
        break;
    case QSystemTrayIcon::Trigger:
        // 单击不执行任何操作（禁用切换窗口状态）
        qDebug() << "Tray icon single-clicked (action disabled)";
        break;
    default:
        break;
    }
}

void TrayManager::onShowMainWindow()
{
    if (m_mainWindow) {
        m_mainWindow->show();
        m_mainWindow->raise();
        m_mainWindow->activateWindow();
        qDebug() << "Main window displayed";
    }
}

void TrayManager::onHideMainWindow()
{
    if (m_mainWindow) {
        m_mainWindow->hide();
        showMessage("显示器管控终端程序", "程序已最小化到系统托盘");
        qDebug() << "Main window hidden to tray";
    }
}

void TrayManager::onClearWhiteList()
{
    int ret = QMessageBox::question(nullptr, "确认操作", 
                                   "确定要清空显示器白名单吗？\n此操作不可撤销。",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        emit clearWhiteListRequested();
        showMessage("白名单管理", "显示器白名单已清空");
        qDebug() << "User requested to clear whitelist";
    }
}

void TrayManager::onSyncWhiteList()
{
    showMessage("白名单管理", "正在同步显示器白名单...");
    emit syncWhiteListRequested();
    qDebug() << "User requested to sync whitelist";
}

void TrayManager::onStartService()
{
    qDebug() << "User requested to start service from tray";

    // 显示确认对话框
    int ret = QMessageBox::question(nullptr, "开启服务",
                                   "确定要开启显示器管控服务吗？\n\n"
                                   "服务将会：\n"
                                   "• 安装Windows服务（如果未安装）\n"
                                   "• 启动服务进程\n"
                                   "• 启用开机自启动\n\n"
                                   "注意：此操作需要管理员权限。",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::Yes);

    if (ret == QMessageBox::Yes) {
        showMessage("服务管理", "正在开启服务，请稍候...");
        emit startServiceRequested();
        qDebug() << "User confirmed to start service";
    } else {
        qDebug() << "User cancelled service start";
    }
}

void TrayManager::onUninstallService()
{
    qDebug() << "User requested to uninstall service from tray";

    // 显示确认对话框
    int ret = QMessageBox::question(nullptr, "卸载服务",
                                   "确定要完全卸载显示器管控服务吗？\n\n"
                                   "此操作将会：\n"
                                   "• 停止并删除Windows服务\n"
                                   "• 删除开机自启动项\n"
                                   "• 清理相关注册表项\n\n"
                                   "注意：此操作不可撤销，需要管理员权限。",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        showMessage("服务管理", "正在卸载服务，请稍候...");
        emit uninstallServiceRequested();
        qDebug() << "User confirmed to uninstall service";
    } else {
        qDebug() << "User cancelled service uninstall";
    }
}

void TrayManager::onDeleteService()
{
    qDebug() << "User requested to delete service from tray";

    // 显示确认对话框
    int ret = QMessageBox::question(nullptr, "删除服务",
                                   "确定要强制删除显示器管控服务吗？\n\n"
                                   "此操作将会：\n"
                                   "• 强制停止并删除Windows服务\n"
                                   "• 强制终止服务进程\n"
                                   "• 清理所有相关注册表项\n"
                                   "• 删除开机自启动项\n"
                                   "• 清理服务残留文件\n\n"
                                   "⚠️ 警告：这是强制删除操作，比卸载更彻底！\n"
                                   "注意：此操作不可撤销，需要管理员权限。",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // 二次确认
        int confirmRet = QMessageBox::warning(nullptr, "最终确认",
                                            "您确定要执行强制删除操作吗？\n\n"
                                            "这将彻底清除服务的所有痕迹，\n"
                                            "包括可能的配置文件和日志。\n\n"
                                            "此操作无法撤销！",
                                            QMessageBox::Yes | QMessageBox::No,
                                            QMessageBox::No);

        if (confirmRet == QMessageBox::Yes) {
            showMessage("服务管理", "正在强制删除服务，请稍候...");
            emit deleteServiceRequested();
            qDebug() << "User confirmed to delete service";
        } else {
            qDebug() << "User cancelled service deletion at final confirmation";
        }
    } else {
        qDebug() << "User cancelled service deletion";
    }
}

void TrayManager::onAbout()
{
    QString aboutText = QString(
        "<h3>显示器管控终端程序 v1.0</h3>"
        "<p>© 2024 显示器管控终端程序<br>"
    ).arg(__DATE__).arg(__TIME__);
    
    QMessageBox::about(nullptr, "关于显示器管控终端程序", aboutText);
}

void TrayManager::onExit()
{
    int ret = QMessageBox::question(nullptr, "确认退出", 
                                   "确定要退出显示器管控终端程序吗？",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        qDebug() << "User requested to exit program";
        QApplication::quit();
    }
}

void TrayManager::onWhiteListUpdated(int count)
{
    m_whiteListCount = count;
    updateTrayStatus(m_currentStatus);
    
    showMessage("白名单更新", QString("白名单已更新，当前包含 %1 个显示器").arg(count));
}

void TrayManager::showMessage(const QString &title, const QString &message, 
                             QSystemTrayIcon::MessageIcon icon)
{
    if (m_trayIcon && isSystemTrayAvailable()) {
        m_trayIcon->showMessage(title, message, icon, 3000);
    }
}
